import React, { useState, useEffect, useMemo } from 'react';
import { Map } from './Map';
import { MessageList } from './MessageList';
import { VoyageDropdown } from './VoyageDropdown';
import { GPSMessage, Voyage, User } from '../types';
import { WebSocketClient } from '../utils/websocket';
import { apiClient } from '../utils/api';
import '../styles/dashboard.css';

interface DashboardProps {
  user: User;
  token: string;
  onLogout: () => void;
  onError: (error: string) => void;
}

export const Dashboard: React.FC<DashboardProps> = ({
  user,
  token,
  onLogout,
  onError
}) => {
  const [messages, setMessages] = useState<GPSMessage[]>([]);
  const [voyages, setVoyages] = useState<Voyage[]>([]);
  const [selectedMessage, setSelectedMessage] = useState<GPSMessage | null>(null);
  const [selectedVoyage, setSelectedVoyage] = useState<string | null>(null);
  const [selectedImei, setSelectedImei] = useState<string>(user.imeiCodes[0] || '');
  const [wsClient, setWsClient] = useState<WebSocketClient | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting');
  const [trackerStatus, setTrackerStatus] = useState<{ [imei: string]: boolean }>({});
  const [showImeiModal, setShowImeiModal] = useState(false);
  const [newImeiCodes, setNewImeiCodes] = useState(user.imeiCodes.join(', '));

  // Date filtering state
  const [fromDate, setFromDate] = useState<string>('');
  const [toDate, setToDate] = useState<string>('');

  // Initialize default date range (today 00:00 to tomorrow 00:00)
  useEffect(() => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Format for datetime-local input (YYYY-MM-DDTHH:MM)
    const formatDateTimeLocal = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}T00:00`;
    };

    setFromDate(formatDateTimeLocal(today));
    setToDate(formatDateTimeLocal(tomorrow));
  }, []);

  // Get filtered voyages based on IMEI and date range
  const filteredVoyages = useMemo(() => {
    console.log(`🚢 Filtering voyages - IMEI: ${selectedImei}, Date: ${fromDate} to ${toDate}`);
    console.log(`📊 Total voyages available: ${voyages.length}`);

    // First filter by IMEI
    let voyagesToFilter = voyages.filter(v => v.imei === selectedImei);
    console.log(`📱 Filtered ${voyagesToFilter.length} voyages for IMEI ${selectedImei}`);

    // Apply date filtering if both dates are set
    if (fromDate && toDate) {
      const fromDateTime = new Date(fromDate);
      const toDateTime = new Date(toDate);

      // Validate date range
      if (fromDateTime > toDateTime) {
        console.log(`⚠️ Invalid date range: ${fromDate} > ${toDate}`);
        return voyagesToFilter; // Return unfiltered by date if range is invalid
      }

      // Filter voyages that have any overlap with the selected date range
      const dateFilteredVoyages = voyagesToFilter.filter(voyage => {
        const voyageStart = new Date(voyage.startTime);
        const voyageEnd = voyage.endTime ? new Date(voyage.endTime) : new Date(); // Use current time for ongoing voyages

        // Check if voyage overlaps with selected date range
        const hasOverlap = voyageStart <= toDateTime && voyageEnd >= fromDateTime;

        if (hasOverlap) {
          console.log(`✅ Voyage ${voyage.id} overlaps with date range (${voyageStart.toISOString()} - ${voyageEnd.toISOString()})`);
        }

        return hasOverlap;
      });

      console.log(`📅 Date filtered ${dateFilteredVoyages.length} voyages from ${voyagesToFilter.length} (${fromDate} to ${toDate})`);
      return dateFilteredVoyages;
    }

    console.log(`📋 Returning ${voyagesToFilter.length} voyages (no date filter)`);
    return voyagesToFilter;
  }, [voyages, selectedImei, fromDate, toDate]);

  // Get filtered messages for display count (filtered by selected IMEI and date range)
  const filteredAndSortedMessages = useMemo(() => {
    console.log(`🔍 Filtering messages - IMEI: ${selectedImei}, Voyage: ${selectedVoyage}, Date: ${fromDate} to ${toDate}`);
    console.log(`📊 Total messages available: ${messages.length}`);

    let messagesToFilter: GPSMessage[] = [];

    // If specific voyage is selected, show only that voyage's messages
    if (selectedVoyage) {
      const voyage = filteredVoyages.find(v => v.id === selectedVoyage);
      if (voyage) {
        console.log(`🚢 Found voyage ${selectedVoyage} with ${voyage.messages.length} messages`);
        messagesToFilter = voyage.messages; // Voyage messages are already for the correct IMEI
      } else {
        console.log(`❌ Voyage ${selectedVoyage} not found in filtered voyages`);
        return [];
      }
    } else {
      // Filter by selected IMEI
      if (selectedImei) {
        messagesToFilter = messages.filter(msg => msg.imei === selectedImei);
        console.log(`📱 Filtered ${messagesToFilter.length} messages for IMEI ${selectedImei}`);
      } else {
        messagesToFilter = messages;
      }
    }

    // Apply date filtering if both dates are set
    if (fromDate && toDate) {
      const fromDateTime = new Date(fromDate);
      const toDateTime = new Date(toDate);

      // Validate date range
      if (fromDateTime > toDateTime) {
        console.log(`⚠️ Invalid date range: ${fromDate} > ${toDate}`);
        return messagesToFilter; // Return unfiltered by date if range is invalid
      }

      const dateFilteredMessages = messagesToFilter.filter(msg => {
        const messageDate = new Date(msg.trackerTimestamp);
        return messageDate >= fromDateTime && messageDate <= toDateTime;
      });

      console.log(`📅 Date filtered ${dateFilteredMessages.length} messages from ${messagesToFilter.length} (${fromDate} to ${toDate})`);
      return dateFilteredMessages;
    }

    console.log(`📋 Returning ${messagesToFilter.length} messages (no date filter)`);
    return messagesToFilter;
  }, [messages, selectedVoyage, filteredVoyages, selectedImei, fromDate, toDate]);

  useEffect(() => {
    const initWebSocket = async () => {
      try {
        const wsUrl = `ws://${window.location.hostname}:8090/ws`;
        const client = new WebSocketClient(wsUrl);
        
        // Set up message handlers
        client.onMessage('auth', (data) => {
          if (data.success) {
            setConnectionStatus('connected');
            // Request initial data
            client.requestVoyages();
            client.requestMessages();
            client.requestTrackerStatus();
          }
        });

        client.onMessage('gps_data', (message: GPSMessage) => {
          // Convert date strings back to Date objects
          const processedMessage = {
            ...message,
            trackerTimestamp: new Date(message.trackerTimestamp),
            serverTimestamp: new Date(message.serverTimestamp)
          };

          // Check if message already exists to avoid duplicates
          setMessages(prev => {
            const existingMessage = prev.find(msg => msg.id === processedMessage.id);
            if (existingMessage) {
              console.log(`⚠️ Message ${processedMessage.id} already exists, skipping duplicate`);
              return prev;
            }
            console.log(`✅ Adding new GPS message for IMEI ${processedMessage.imei}, status: ${processedMessage.status}`);
            return [processedMessage, ...prev];
          });

          // Also update voyages list when we receive new GPS data
          // Update for trip start/end AND for ongoing trips to show real-time polylines
          if (processedMessage.status === 'Inizio' || processedMessage.status === 'Fine' || processedMessage.status === 'GPS Fixed') {
            console.log(`🚢 GPS data received (${processedMessage.status}), requesting updated voyage list for real-time polyline updates`);
            client.requestVoyages();
          }
        });

        client.onMessage('voyage_list', (voyageList: Voyage[]) => {
          const processedVoyages = voyageList.map(voyage => ({
            ...voyage,
            startTime: new Date(voyage.startTime),
            endTime: voyage.endTime ? new Date(voyage.endTime) : undefined,
            messages: voyage.messages.map(msg => ({
              ...msg,
              trackerTimestamp: new Date(msg.trackerTimestamp),
              serverTimestamp: new Date(msg.serverTimestamp)
            }))
          }));
          console.log(`🚢 Received ${processedVoyages.length} voyages from server`);
          setVoyages(processedVoyages);
        });

        client.onMessage('message_list', (messageList: GPSMessage[]) => {
          const processedMessages = messageList.map(msg => ({
            ...msg,
            trackerTimestamp: new Date(msg.trackerTimestamp),
            serverTimestamp: new Date(msg.serverTimestamp)
          }));
          console.log(`📨 Received ${processedMessages.length} messages from server`);
          setMessages(processedMessages);
        });

        client.onMessage('tracker_status', (status: { [imei: string]: boolean }) => {
          setTrackerStatus(status);
        });

        client.onError((error) => {
          onError(`WebSocket error: ${error}`);
          setConnectionStatus('disconnected');
        });

        // Connect and authenticate
        await client.connect();
        client.authenticate(token);
        setWsClient(client);

        // Set up periodic tracker status updates
        const statusInterval = setInterval(() => {
          if (client.isConnected()) {
            client.requestTrackerStatus();
          }
        }, 30000); // Update every 30 seconds

        return () => clearInterval(statusInterval);

      } catch (error) {
        onError('Failed to connect to server');
        setConnectionStatus('disconnected');
      }
    };

    initWebSocket();

    return () => {
      if (wsClient) {
        wsClient.disconnect();
      }
    };
  }, [token, onError]);

  // Effect to handle IMEI changes and refresh data
  useEffect(() => {
    if (wsClient && wsClient.isConnected() && selectedImei) {
      console.log(`🔄 Selected IMEI changed to ${selectedImei}, refreshing data...`);
      // Small delay to ensure the state has been updated
      const timeoutId = setTimeout(() => {
        wsClient.requestVoyages();
        wsClient.requestMessages();
        wsClient.requestTrackerStatus();
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [selectedImei, wsClient]);



  // Effect to handle data clearing when no messages for selected IMEI
  useEffect(() => {
    if (selectedImei && filteredAndSortedMessages.length === 0) {
      console.log(`🧹 No messages found for IMEI ${selectedImei}, clearing selections`);
      setSelectedVoyage(null);
      setSelectedMessage(null);
    }
  }, [selectedImei, filteredAndSortedMessages]);

  const handleVoyageSelect = (voyageId: string | null) => {
    console.log(`🚢 Voyage selection changed to: ${voyageId || 'all voyages'}`);
    setSelectedVoyage(voyageId);
    setSelectedMessage(null); // Clear message selection when changing voyage

    // Note: We don't need to request messages from server since voyage data
    // already contains all the messages. The filtering happens client-side.
    if (voyageId) {
      const voyage = voyages.find(v => v.id === voyageId);
      if (voyage) {
        console.log(`✅ Selected voyage has ${voyage.messages.length} messages`);
      }
    } else {
      console.log(`📋 Showing all voyages for IMEI ${selectedImei}`);
    }
  };

  const handleMessageSelect = (message: GPSMessage) => {
    setSelectedMessage(message);
  };

  const handleImeiChange = (newImei: string) => {
    console.log(`🔄 IMEI changed from ${selectedImei} to ${newImei}`);
    setSelectedImei(newImei);
    setSelectedVoyage(null); // Reset voyage selection when changing IMEI
    setSelectedMessage(null); // Reset message selection when changing IMEI

    // Request updated data for the new IMEI
    if (wsClient && wsClient.isConnected()) {
      console.log(`📡 Requesting updated data for IMEI ${newImei}...`);
      wsClient.requestVoyages();
      wsClient.requestMessages();
      wsClient.requestTrackerStatus();
    }
  };

  const handleFromDateChange = (newFromDate: string) => {
    console.log(`📅 From date changed to: ${newFromDate}`);
    setFromDate(newFromDate);
  };

  const handleToDateChange = (newToDate: string) => {
    console.log(`📅 To date changed to: ${newToDate}`);
    setToDate(newToDate);
  };

  // Validate date range
  const isDateRangeValid = useMemo(() => {
    if (!fromDate || !toDate) return true;
    return new Date(fromDate) <= new Date(toDate);
  }, [fromDate, toDate]);

  // Reset selected voyage if it's not in the filtered voyages list
  useEffect(() => {
    if (selectedVoyage && !filteredVoyages.find(v => v.id === selectedVoyage)) {
      console.log(`🔄 Selected voyage ${selectedVoyage} not in filtered list, resetting selection`);
      setSelectedVoyage(null);
      setSelectedMessage(null);
    }
  }, [selectedVoyage, filteredVoyages]);

  const handleUpdateImeiCodes = async () => {
    try {
      const imeiArray = newImeiCodes
        .split(',')
        .map(code => code.trim())
        .filter(code => code.length > 0);

      await apiClient.updateImeiCodes({
        token,
        imeiCodes: imeiArray
      });

      // Update local user state
      user.imeiCodes = imeiArray;
      setShowImeiModal(false);
      
      // Reconnect WebSocket to get updated data
      if (wsClient) {
        wsClient.disconnect();
        setTimeout(() => {
          window.location.reload(); // Simple way to reinitialize with new IMEI codes
        }, 1000);
      }
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Failed to update IMEI codes');
    }
  };

  const getTrackerStatusColor = () => {
    if (!selectedImei) return 'text-secondary';
    const isConnected = trackerStatus[selectedImei];
    return isConnected ? 'text-success' : 'text-error';
  };

  const getTrackerStatusText = () => {
    if (!selectedImei) return 'Nessun tracker';
    const isConnected = trackerStatus[selectedImei];
    return isConnected ? 'Tracker Connesso' : 'Tracker Disconnesso';
  };

  return (
    <div className="dashboard-container">
      {/* Header */}
      <header className="dashboard-header">
        <div className="header-content">
          <div className="header-left">
            <div className="app-logo">
              <div className="logo-icon">🌍</div>
              <h1 className="app-title">ECOTrac</h1>
            </div>
            <div className="header-info">
              <span className="user-info">Welcome, {user.username}</span>
              <span className={`connection-status ${getTrackerStatusColor()}`}>
                ● {getTrackerStatusText()}
              </span>
            </div>
          </div>
          <div className="header-right">
            <button
              onClick={() => setShowImeiModal(true)}
              className="header-btn"
              title="Manage IMEI Codes"
            >
              ⚙️ Settings
            </button>
            <button
              onClick={onLogout}
              className="header-btn logout-btn"
              title="Logout"
            >
              🚪 Exit
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="dashboard-main">
        {/* Map Panel - Top 60-70% */}
        <div className="map-panel">
          <div className="panel-header">
            <div className="panel-title">
              📍 Mappa Posizioni
            </div>
            <div className="panel-controls">
              <select
                value={selectedImei}
                onChange={(e) => handleImeiChange(e.target.value)}
                className="imei-selector"
              >
                {user.imeiCodes.map(imei => (
                  <option key={imei} value={imei}>
                    {imei}
                  </option>
                ))}
              </select>
              <VoyageDropdown
                voyages={filteredVoyages}
                selectedImei={selectedImei}
                selectedVoyage={selectedVoyage}
                onVoyageSelect={handleVoyageSelect}
              />
            </div>
          </div>
          <div className="map-container">
            <Map
              messages={filteredAndSortedMessages}
              selectedMessage={selectedMessage}
              selectedVoyage={selectedVoyage}
              selectedImei={selectedImei}
              voyages={filteredVoyages}
              onMessageSelect={handleMessageSelect}
            />
          </div>
        </div>

        {/* Message List Panel - Bottom 30-40% */}
        <div className="message-panel">
          <div className="panel-header">
            <div className="panel-title">
              📊 Dettagli
            </div>
            <div className="panel-header-controls">
              {/* Date Filter Controls */}
              <div className="date-filter-container">
                <div className="date-filter-row">
                  <div className="date-filter-group">
                    <label htmlFor="from-date" className="date-filter-label">
                      Da:
                    </label>
                    <input
                      id="from-date"
                      type="datetime-local"
                      value={fromDate}
                      onChange={(e) => handleFromDateChange(e.target.value)}
                      className={`date-filter-input ${!isDateRangeValid ? 'invalid' : ''}`}
                    />
                  </div>
                  <div className="date-filter-group">
                    <label htmlFor="to-date" className="date-filter-label">
                      A:
                    </label>
                    <input
                      id="to-date"
                      type="datetime-local"
                      value={toDate}
                      onChange={(e) => handleToDateChange(e.target.value)}
                      className={`date-filter-input ${!isDateRangeValid ? 'invalid' : ''}`}
                    />
                  </div>
                  {!isDateRangeValid && (
                    <div className="date-filter-error">
                      ⚠️ Data di inizio deve essere precedente alla data di fine
                    </div>
                  )}
                </div>
              </div>
              <div className="message-count">
                {filteredAndSortedMessages.length} messaggi
              </div>
            </div>
          </div>
          <div className="message-container">
            <MessageList
              messages={filteredAndSortedMessages}
              selectedMessage={selectedMessage}
              selectedVoyage={selectedVoyage}
              selectedImei={selectedImei}
              voyages={voyages}
              onMessageSelect={handleMessageSelect}
            />
          </div>
        </div>
      </div>

      {/* IMEI Management Modal */}
      {showImeiModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="card w-full max-w-md">
            <div className="card-header">
              <h3 className="card-title">Manage IMEI Codes</h3>
            </div>
            <div className="card-body">
              <div className="mb-4">
                <label className="block text-sm font-semibold mb-2">
                  IMEI Codes (comma-separated)
                </label>
                <textarea
                  value={newImeiCodes}
                  onChange={(e) => setNewImeiCodes(e.target.value)}
                  className="input h-24 resize-none"
                  placeholder="123456789012345, 987654321098765"
                />
                <p className="text-sm text-secondary mt-1">
                  Enter the IMEI codes of GPS trackers you want to monitor
                </p>
              </div>
              <div className="flex gap-2">
                <button
                  onClick={handleUpdateImeiCodes}
                  className="btn btn-primary flex-1"
                >
                  Update
                </button>
                <button
                  onClick={() => setShowImeiModal(false)}
                  className="btn btn-secondary flex-1"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
